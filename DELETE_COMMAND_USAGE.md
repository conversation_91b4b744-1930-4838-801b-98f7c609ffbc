# Task Scheduler CLI 删除功能使用说明

## 功能概述

task_scheduler的CLI工具现在支持删除指定任务ID的功能。删除操作遵循以下规则：

1. **如果要删除的任务不是其他任务的依赖**：可以直接删除
2. **如果要删除的任务是其他任务的依赖，但所有依赖任务都已完成**：可以删除
3. **如果要删除的任务是其他任务的依赖，且存在未完成的依赖任务**：删除失败

## 命令语法

```bash
python -m task_scheduler.cli delete -f <任务文件路径> <任务ID>
```

## 参数说明

- `-f, --file`: 任务状态文件路径（必需）
- `<任务ID>`: 要删除的任务的唯一标识符（必需）

## 使用示例

### 1. 删除没有依赖任务的任务

```bash
python -m task_scheduler.cli delete -f tasks.json abc123-def456-ghi789
```

**输出示例：**
```
信息: 任务 '示例任务' (ID: abc123-def456-ghi789) 没有其他任务依赖，可以安全删除
日志: 删除检查通过 - 任务 '示例任务' (ID: abc123-def456-ghi789) 没有依赖任务
成功删除任务: 示例任务 (ID: abc123-def456-ghi789)
日志: 删除操作成功 - 任务 '示例任务' (ID: abc123-def456-ghi789) 已从系统中移除
```

### 2. 删除有已完成依赖任务的任务

```bash
python -m task_scheduler.cli delete -f tasks.json xyz789-abc123-def456
```

**输出示例：**
```
信息: 任务 '基础任务' (ID: xyz789-abc123-def456) 的所有依赖任务都已完成，可以安全删除
  - 依赖任务1 (ID: dep1-id) [completed]
  - 依赖任务2 (ID: dep2-id) [completed]
日志: 删除检查通过 - 任务 '基础任务' (ID: xyz789-abc123-def456) 的 2 个依赖任务都已完成
成功删除任务: 基础任务 (ID: xyz789-abc123-def456)
日志: 删除操作成功 - 任务 '基础任务' (ID: xyz789-abc123-def456) 已从系统中移除
```

### 3. 删除有未完成依赖任务的任务（失败）

```bash
python -m task_scheduler.cli delete -f tasks.json failed-delete-id
```

**输出示例：**
```
错误: 无法删除任务 '重要任务' (ID: failed-delete-id)，因为以下任务依赖于它且尚未完成:
  - 运行中任务 (ID: running-task-id) [running]
  - 等待中任务 (ID: waiting-task-id) [waiting]

以下依赖任务已完成:
  - 已完成任务 (ID: completed-task-id) [completed]

请等待所有依赖任务完成后再删除此任务
日志: 删除操作失败 - 任务 '重要任务' (ID: failed-delete-id) 存在 2 个未完成的依赖任务
```

### 4. 删除不存在的任务（失败）

```bash
python -m task_scheduler.cli delete -f tasks.json non-existent-id
```

**输出示例：**
```
错误: 未找到任务 ID: non-existent-id
日志: 删除操作失败 - 任务 non-existent-id 不存在
```

## 日志记录

删除功能会输出详细的日志信息，包括：

- **删除检查通过**：当任务可以安全删除时
- **删除操作成功**：当任务成功删除时
- **删除操作失败**：当删除失败时，包含失败原因
- **删除操作取消**：当用户取消删除操作时

## 安全特性

1. **智能删除**：可以删除的任务直接删除，无需用户确认
2. **依赖检查**：自动检查任务依赖关系，防止破坏任务流程
3. **详细反馈**：提供清晰的错误信息和操作状态
4. **状态感知**：只有当依赖任务都已完成时才允许删除

## 相关命令

- `list`: 查看所有任务列表
- `show <task_id>`: 查看特定任务的详细信息，包括依赖关系
- `graph`: 显示任务依赖关系图

## 注意事项

1. 可以删除的任务会直接删除，无需用户确认
2. 删除任务前建议先使用 `show` 命令查看任务的依赖关系
3. 如果需要删除有未完成依赖任务的任务，请先等待或手动完成这些依赖任务
4. 任务文件会自动备份，但建议在重要操作前手动备份任务文件
