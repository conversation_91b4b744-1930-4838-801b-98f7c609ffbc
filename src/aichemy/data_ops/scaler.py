import functools
import re
from enum import Enum
from typing import Mapping, MutableMapping, <PERSON>ple

import numpy as np
import pandas as pd

from ..base import Model
from ..utils import (
    multi_csmad_for_df,
    multi_csmax_for_df,
    multi_csmean_for_df,
    multi_csmin_for_df,
    multi_cspercentile_for_df,
    multi_csstd_for_df,
)
from .base import ACTransformer


class ScaleType(Enum):
    NONE = "none"
    GLOBAL = "global"
    CROSS_SECTIONAL = "cs"
    MULTI_CROSS_SECTIONAL = "mcs"


class Scaler(Model):
    """
    Input      -------->        Output\n
    -------------------------------------
    NaN        --------->        NaN\n
    Number     --------->        Number
    要求传入的df中没有inf
    """

    _scale_type: ScaleType
    _kwargs: Mapping
    _params: <PERSON><PERSON>

    def __init__(self, scale_type: ScaleType, **kwargs) -> None:
        self._scale_type = scale_type
        self._kwargs = kwargs
        self._params = ()
        """保存参数，比如ZScoreScaler的mean和std，如果是截面或滚动计算的，此处为空"""

        if scale_type == ScaleType.MULTI_CROSS_SECTIONAL:
            if "windows" not in self._kwargs:
                raise ValueError("MULTI_CROSS_SECTIONAL操作必须指定windows")

    @property
    def drop_windows_length(self) -> int:
        """滚动计算时，前面几个数据是无效的，其他情况下，不需要丢弃数据"""
        if self._scale_type == ScaleType.MULTI_CROSS_SECTIONAL:
            return self._kwargs["windows"] - 1
        else:
            return 0

    @property
    def is_cross_sectional_scaling(self) -> bool:
        return self._scale_type in [ScaleType.CROSS_SECTIONAL, ScaleType.MULTI_CROSS_SECTIONAL]

    @property
    def is_valid(self) -> bool:
        """scaler是否有效，如果参数（比如ZScoreScaler的mean和std）中有inf或者nan，就是无效的"""
        return bool(self.is_cross_sectional_scaling or not any([(pd.isna(i) or np.isinf(i)) for i in self._params]))

    @property
    def params(self) -> Tuple:
        return self._params

    def transform(self, df: pd.DataFrame) -> pd.DataFrame:
        """transform操作需要保证传入的df中没有inf，另外还要保证返回的df中也没有inf，原先的number的位置也是number"""
        raise NotImplementedError

    def fit_transform(self, df: pd.DataFrame) -> pd.DataFrame:
        self.fit(df)
        return self.transform(df)

    @property
    def default(self) -> float: ...
    def fit(self, df: pd.DataFrame) -> None: ...


def replace_inf(fnc):
    """装饰器，把入参df中的inf替换成nan"""

    @functools.wraps(fnc)
    def wrapper(self, df):
        df = df.replace([np.inf, -np.inf], np.nan, inplace=False)
        return fnc(self, df)

    return wrapper


class MaxMinScaler(Scaler):
    _params: Tuple[float, float]

    def __init__(self, scale_type: ScaleType, **kwargs) -> None:
        super().__init__(scale_type=scale_type, **kwargs)
        self._params = (np.nan, np.nan)

    @replace_inf
    def fit(self, df: pd.DataFrame):
        if not self.is_cross_sectional_scaling:
            self._params = (np.nanmin(df), np.nanmax(df))

    @replace_inf
    def transform(self, df: pd.DataFrame) -> pd.DataFrame:
        if self.is_cross_sectional_scaling:
            if self._scale_type == ScaleType.CROSS_SECTIONAL:
                min_: pd.Series = df.min(axis=1, skipna=True)
                max_: pd.Series = df.max(axis=1, skipna=True)
            else:
                min_ = multi_csmin_for_df(df, self._kwargs["windows"])
                max_ = multi_csmax_for_df(df, self._kwargs["windows"])
            ret = df.sub(min_, axis=0).div(max_ - min_, axis=0)
        else:
            ret = (df - self._params[0]) / (self._params[1] - self._params[0])
        ret.replace([np.inf, -np.inf], np.nan, inplace=True)
        ret[ret.isna() & (~df.isna())] = 0.5
        if self._scale_type == ScaleType.MULTI_CROSS_SECTIONAL:
            ret.iloc[: self.drop_windows_length] = np.nan
        return ret

    @property
    def default(self) -> float:
        return 0.5


class ZScoreScaler(Scaler):
    _params: Tuple[float, float]

    def __init__(self, scale_type: ScaleType, **kwargs) -> None:
        super().__init__(scale_type=scale_type, **kwargs)
        self._params = (np.nan, np.nan)

    @replace_inf
    def fit(self, df: pd.DataFrame):
        if not self.is_cross_sectional_scaling:
            self._params = (float(np.nanmean(df)), float(np.nanstd(df)))

    @replace_inf
    def transform(self, df: pd.DataFrame) -> pd.DataFrame:
        if self.is_cross_sectional_scaling:
            if self._scale_type == ScaleType.CROSS_SECTIONAL:
                mean_ = df.mean(axis=1, skipna=True)
                std_ = df.std(axis=1, skipna=True)
            else:
                mean_ = multi_csmean_for_df(df, self._kwargs["windows"])
                std_ = multi_csstd_for_df(df, self._kwargs["windows"])
            ret = df.sub(mean_, axis=0).div(std_, axis=0)
        else:
            ret = (df - self._params[0]) / self._params[1]
        ret.replace([np.inf, -np.inf], np.nan, inplace=True)
        ret[ret.isna() & (~df.isna())] = 0
        if self._scale_type == ScaleType.MULTI_CROSS_SECTIONAL:
            ret.iloc[: self.drop_windows_length] = np.nan
        return ret

    @property
    def default(self) -> float:
        return 0


class RobustZScoreScaler(Scaler):
    _params: Tuple[float, float]

    def __init__(self, scale_type: ScaleType, **kwargs) -> None:
        super().__init__(scale_type=scale_type, **kwargs)
        self._params = (np.nan, np.nan)

    @replace_inf
    def fit(self, df: pd.DataFrame):
        if not self.is_cross_sectional_scaling:
            median_ = float(np.nanmedian(df))
            mad_ = float(np.nanmedian((df - median_).abs()))
            self._params = (median_, mad_)

    @replace_inf
    def transform(self, df: pd.DataFrame) -> pd.DataFrame:
        if self.is_cross_sectional_scaling:
            if self._scale_type == ScaleType.CROSS_SECTIONAL:
                median_ = df.median(axis=1, skipna=True)
                mad_ = df.sub(median_, axis=0).abs().median(axis=1, skipna=True)
            else:
                median_ = multi_cspercentile_for_df(df, self._kwargs["windows"], 50)
                mad_ = multi_csmad_for_df(df, self._kwargs["windows"])
            ret = df.sub(median_, axis=0).div(mad_, axis=0)
        else:
            ret = (df - self._params[0]) / self._params[1]
        ret.replace([np.inf, -np.inf], np.nan, inplace=True)
        ret[ret.isna() & (~df.isna())] = 0
        if self._scale_type == ScaleType.MULTI_CROSS_SECTIONAL:
            ret.iloc[: self.drop_windows_length] = np.nan
        return ret

    @property
    def default(self) -> float:
        return 0


class RobustScaler(Scaler):
    _params: Tuple[float, float]

    def __init__(self, scale_type: ScaleType, **kwargs) -> None:
        super().__init__(scale_type=scale_type, **kwargs)
        self._params = (np.nan, np.nan)

    @replace_inf
    def fit(self, df: pd.DataFrame):
        if not self.is_cross_sectional_scaling:
            median_ = float(np.nanmedian(df))
            iqr_ = float(np.nanpercentile(df, 75) - np.nanpercentile(df, 25))
            self._params = (median_, iqr_)

    @replace_inf
    def transform(self, df: pd.DataFrame) -> pd.DataFrame:
        if self.is_cross_sectional_scaling:
            if self._scale_type == ScaleType.CROSS_SECTIONAL:
                median_ = df.median(axis=1, skipna=True)
                iqr_ = df.quantile(0.75, axis=1) - df.quantile(0.25, axis=1)
            else:
                median_ = multi_cspercentile_for_df(df, self._kwargs["windows"], 50)
                iqr_ = multi_cspercentile_for_df(df, self._kwargs["windows"], 75) - multi_cspercentile_for_df(
                    df, self._kwargs["windows"], 25
                )
            ret = df.sub(median_, axis=0).div(iqr_, axis=0)
        else:
            ret = (df - self._params[0]) / self._params[1]
        ret.replace([np.inf, -np.inf], np.nan, inplace=True)
        ret[ret.isna() & (~df.isna())] = 0
        if self._scale_type == ScaleType.MULTI_CROSS_SECTIONAL:
            ret.iloc[: self.drop_windows_length] = np.nan
        return ret

    @property
    def default(self) -> float:
        return 0


# TODO
class RankScaler(Scaler):
    def fit(self, *args, **kwargs): ...
    def transform(self, df: pd.DataFrame) -> pd.DataFrame:
        if self.is_cross_sectional_scaling:
            return df.rank(axis=1)
        else:
            return df.stack().rank().unstack()  # type: ignore

    @property
    def default(self) -> float:
        raise NotImplementedError


class NoneScaler(Scaler):
    def __init__(self) -> None:
        super().__init__(scale_type=ScaleType.NONE)

    def transform(self, df: pd.DataFrame) -> pd.DataFrame:
        return df.copy(deep=True)

    @property
    def default(self) -> float:
        return np.nan

    @property
    def is_valid(self) -> bool:
        return True


def scaler_factory(method_) -> Scaler:
    tmp = re.search(r"^(?:(cs)_)?(?:(\d+)_)?(maxmin|zscore|robustzscore|robustscaler|rank|none)$", method_)
    if tmp is None:
        raise ValueError(f"Invalid scaler: {method_}")
    cs, windows, method = tmp.groups()
    if (
        method is None
        or (method == "none" and not (cs is None and windows is None))
        or (cs is None and windows is not None)
    ):
        raise ValueError(f"Invalid scaler: {method_}")

    scaler_mapping = {
        "maxmin": MaxMinScaler,
        "zscore": ZScoreScaler,
        "robustzscore": RobustZScoreScaler,
        "robustscaler": RobustScaler,
        "rank": RankScaler,
        "none": NoneScaler,
    }
    if cs is None:
        return scaler_mapping[method](scale_type=ScaleType.GLOBAL)
    elif windows is None:
        return scaler_mapping[method](scale_type=ScaleType.CROSS_SECTIONAL)
    else:
        return scaler_mapping[method](scale_type=ScaleType.MULTI_CROSS_SECTIONAL, windows=int(windows))


class ACScaler(ACTransformer):
    method_x: str
    method_y: str
    fillna: bool
    scaler: Mapping[str, Scaler]

    def __init__(self, method_x: str = "none", method_y: str = "none", fillna: bool = False) -> None:
        self.method_x = method_x
        self.method_y = method_y
        self.fillna = fillna

        self.scaler = {}

    def fit(self, df_dict: Mapping[str, pd.DataFrame]):
        self.scaler = {}
        split = df_dict.get("[split]", None)

        for key in df_dict.keys():
            if "[x]" in key:
                self.scaler[key] = scaler_factory(self.method_x)
                if not self.scaler[key].is_cross_sectional_scaling:
                    if split is not None:
                        self.scaler[key].fit(df_dict[key][split])
                    else:
                        self.scaler[key].fit(df_dict[key])

            if "[y]" in key:
                self.scaler[key] = scaler_factory(self.method_y)
                if not self.scaler[key].is_cross_sectional_scaling:
                    if split is not None:
                        self.scaler[key].fit(df_dict[key][split])
                    else:
                        self.scaler[key].fit(df_dict[key])

    def transform(self, df_dict: MutableMapping[str, pd.DataFrame], inplace: bool) -> MutableMapping[str, pd.DataFrame]:
        if len(set(self.scaler.keys()).difference(df_dict.keys())) > 0:
            raise ValueError("scaler key > df_dict key")

        should_be_filled = None
        if self.fillna:
            for key in df_dict.keys():
                if key.startswith("[x]") or key.startswith("[y]"):
                    if should_be_filled is None:
                        should_be_filled = ~df_dict[key].isna()
                    else:
                        should_be_filled = should_be_filled | (~df_dict[key].isna())

        ret = {} if not inplace else df_dict
        max_drop_windows_length = 0
        for key in df_dict.keys():
            if key.startswith("[x]"):
                # 如果[x]有对应的scaler且是有效的，就进行scale; 否则这个数据不加入到结果中
                if key in self.scaler and self.scaler[key].is_valid:
                    max_drop_windows_length = max(max_drop_windows_length, self.scaler[key].drop_windows_length)
                    if not isinstance(self.scaler[key], NoneScaler):
                        ret[key] = self.scaler[key].transform(df_dict[key])
                        if self.fillna and should_be_filled is not None:
                            ret[key].fillna(self.scaler[key].default, inplace=True)
                            ret[key].where(should_be_filled, inplace=True)
                        tmp = np.isposinf(df_dict[key])
                        if np.any(tmp):
                            ret[key][tmp] = np.inf
                        tmp = np.isneginf(df_dict[key])
                        if np.any(tmp):
                            ret[key][tmp] = -np.inf
                    else:
                        ret[key] = df_dict[key]

            elif key.startswith("[y]"):
                # 如果[y]有对应的scaler且是有效的，就进行scale; 否则这个数据不加入到结果中
                if key in self.scaler and self.scaler[key].is_valid:
                    max_drop_windows_length = max(max_drop_windows_length, self.scaler[key].drop_windows_length)
                    if not isinstance(self.scaler[key], NoneScaler):
                        ret[key] = self.scaler[key].transform(df_dict[key])
                        tmp = np.isposinf(df_dict[key])
                        if np.any(tmp):
                            ret[key][tmp] = np.inf
                        tmp = np.isneginf(df_dict[key])
                        if np.any(tmp):
                            ret[key][tmp] = -np.inf
                    else:
                        ret[key] = df_dict[key]

            else:
                ret[key] = df_dict[key]

        for key in ret.keys():
            ret[key].drop(ret[key].index[:max_drop_windows_length], axis=0, inplace=True)
        return ret

    def fit_transform(
        self, df_dict: MutableMapping[str, pd.DataFrame], inplace: bool
    ) -> MutableMapping[str, pd.DataFrame]:
        self.fit(df_dict)
        return self.transform(df_dict, inplace)

    def __str__(self) -> str:
        return f"scaler_x: {self.method_x}, scaler_y: {self.method_y}, fillna: {self.fillna}"
